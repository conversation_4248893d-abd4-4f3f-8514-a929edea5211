"use client";
import SmallDisplay from "@/components/small-display";
import { ChatForm } from "@/components/chat";
import { Button } from "@/components/ui/button";
import { useManageChat, Message } from "@/hooks/use-manage-chat";
import { useEffect, useState } from "react";
import { TextSelectionActions } from "@/components/text-selection";
import { useCallback } from "react";
import { basicActions, actions } from "@/lib/actions-text-selections";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { format } from 'date-fns';
import { Trash2, RefreshCw, PlusSquare } from "lucide-react"

export default function Page() {
  const { startNewChat, isOpen, toggleOpen, setMessages, setChatSectionId, setSelectedGrammarRulesArray, setSelectedTopicsArray, setGrammarRuleCount } = useManageChat();
  const [chatSections, setChatSections] = useState<any[]>([]);

  useEffect(() => {
    toggleOpen(false);
    fetchChatSections();
  }, []);

  const fetchChatSections = async () => {
    try {
      const response = await fetch('/api/chat-sections');
      if (!response.ok) {
        throw new Error('Failed to fetch chat sections');
      }
      const data = await response.json();
      setChatSections(data);
    } catch (error) {
      console.error("Error fetching chat sections:", error);
    }
  };



  const loadChatSection = (section: any) => {
    const loadedMessages: Message[] = section.messages.map((msg: any) => ({
      role: msg.role,
      content: msg.content,
      correction: msg.correction || undefined, // Load correction data
      suggestion: msg.suggestion || undefined, // Load suggestion data
    }));
    setMessages(loadedMessages);
    setChatSectionId(section.id);
    setSelectedGrammarRulesArray(section.grammarRuleSections.map((grs: any) => ({
      value: grs.grammarRule.value,
      weight: grs.weight,
      tags: grs.grammarRule.tags,
    })));
    // Set selected topics
    setSelectedTopicsArray(section.chatSectionTopics.map((cst: any) => cst.customChatSetting.value));
    // Set grammar rule count
    setGrammarRuleCount(section.grammarRuleCount || 1);
  };

  const handleDeleteSection = async (id: string) => {
    if (window.confirm("Are you sure you want to delete this chat section?")) {
      try {
        const response = await fetch(`/api/chat-sections/${id}`, {
          method: 'DELETE',
        });
        if (!response.ok) {
          throw new Error('Failed to delete chat section');
        }
        fetchChatSections(); // Re-fetch sections after deletion
        // Clear current chat if the deleted section was the one currently loaded
        setMessages([]);
        setChatSectionId(null);
        setSelectedGrammarRulesArray([]);
        setSelectedTopicsArray([]);
        setGrammarRuleCount(1);
      } catch (error) {
        console.error("Error deleting chat section:", error);
      }
    }
  };

  return (
    <div className="flex h-svh w-full">
      {/* Left Column: Past Chat Sections */}
      <div className="w-1/4 border-r p-4 flex flex-col">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Past Chats</h2>
          <div>
            <Button variant="ghost" size="icon" onClick={() => startNewChat()}>
              <PlusSquare className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon" onClick={fetchChatSections}>
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <ScrollArea className="flex-1 pr-2">
          {chatSections.length === 0 ? (
            <p className="text-sm text-muted-foreground">No past chat sections found.</p>
          ) : (
            <div className="space-y-3">
              {chatSections.map((section) => (
                <Card key={section.id} className="group relative cursor-pointer hover:bg-accent">
                  <div onClick={() => loadChatSection(section)}>
                    <CardHeader className="p-3 pb-1">
                      <CardTitle className="text-base truncate">{section.content || `Chat ${format(new Date(section.createdAt), 'PPP p')}`}</CardTitle>
                    </CardHeader>
                    <CardContent className="p-3 pt-0 text-sm text-muted-foreground">
                      <p>{new Date(section.createdAt).toLocaleDateString()}</p>
                      {/* {section.grammarRuleSections.length > 0 && (
                        <div className="mt-1 text-xs">
                          <span className="font-medium">Rules:</span> {section.grammarRuleSections.map((grs: any) => grs.grammarRule.value).join(', ')}
                        </div>
                      )} */}
                      {section.chatSectionTopics.length > 0 && (
                        <div className="mt-1 text-xs">
                          <span className="font-medium">Topics:</span> {section.chatSectionTopics.map((cst: any) => cst.customChatSetting.value).join(', ')}
                        </div>
                      )}
                    </CardContent>
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={() => handleDeleteSection(section.id)}
                  >
                    <Trash2 className="h-4 w-4 text-red-500" />
                  </Button>
                </Card>
              ))}
            </div>
          )}
        </ScrollArea>
      </div>

      {/* Right Column: Chat Interface */}
      <div className="flex flex-col h-full flex-1 items-center">
        <div className="flex flex-col h-full w-[80%]"> {/* Adjusted width for chat form */}
          <div className="flex-grow p-2">
            <SmallDisplay />
          </div>
          <div className="h-[87%]">
            <ChatForm />
          </div>
        </div>
      </div>
    </div>
  );
}
