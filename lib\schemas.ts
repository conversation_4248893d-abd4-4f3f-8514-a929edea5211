import {z} from 'zod';

export const flexibleTranslationSchema = z.object({
  translations:
    // The key is a string (the language), the value is an array of translation objects
    z.array(
      z.object({
        text: z.string(),
        ipa: z.string()
      })
    )

});

export const schemaDiscovery = z.object({
  primaryMeaning: z.array(
    z.object({
      meaning: z.string().describe("The primary meaning of the word."),
      translation: z.string().describe("French translation of the primary meaning."),
    })
  ),

  exampleSentences: z
    .array(
      z.object({
        sentence: z.string().describe("Example sentence."),
        translation: z.string().describe("French translation of the example sentence."),
      })
    )
    .min(1)
    .max(3)
    .describe("Example sentences that demonstrate the usage of the word."),

  otherMeanings: z
    .array(
      z.object({
        meaning: z.string().describe("Other meanings of the word."),
        example: z.string().describe("One example sentence for this meaning."),
      })
    )
    .describe("Additional meanings of the word."),

  synonyms: z
    .array(z.string())
    .describe("A few synonyms of the word."),

  usageFrequency: z
    .object({
      speaking:z.object({
        frequency: z
          .number()
          .min(1)
          .max(10)
          .describe("How commonly the word is used in speaking on a scale of 1 to 10."),
        examples: z
          .array(z.string())
          .describe("Examples of how the word is used in speaking."),
        mostUsedWord:z.object({
          word:z.string().describe("the most used word to express the main meaning of the word by speaking"),
          example:z.string().describe("example of how this word is used in speaking")
        }).optional().describe("if the word is not the most used word to express the main meaning of the word by speaking,provide givethe most used word to express the main meaning of the word by speaking here")
      }),
      writing:z.object({
        frequency: z
          .number()
          .min(1)
          .max(10)
          .describe("How commonly the word is used in writing on a scale of 1 to 10."),
        examples: z
          .array(z.string())
          .describe("Examples of how the word is used in writing."),
        mostUsedWord:z.object({
          word:z.string().describe("the most used word to express the main meaning of the word by writing"),
          example:z.string().describe("example of how this word is used in writing")
        }).optional().describe("if the word is not the most used word to express the main meaning of the word by writing,provide givethe most used word to express the main meaning of the word by writing here")
      }),
    })
    .describe("Usage frequency of the word in both speaking and writing."),
});

export const schemaThemeDiscover =z.object({
  verbs:z.array(z.object({word: z.string().describe("The word being described."),

    primaryMeaning: z.array(
      z.object({
        meaning: z.string().describe("The primary meaning of the word."),
        translation: z.string().describe("French translation of the primary meaning."),
      })
    ),
  
    exampleSentences: z.array(
      z.object({
        sentence: z.string().describe("Example sentence."),
        translation: z.string().describe("French translation of the example sentence."),
      })
    ),
  
    otherMeanings: z.array(
      z.object({
        meaning: z.string().describe("Other meanings of the word."),
        example: z.string().describe("One example sentence for this meaning."),
      })
    ),
  
    synonyms: z.array(z.string()).describe("A few synonyms of the word."),
  
    usageFrequency: z.object({
      speaking: z.object({
        frequency: z.number().min(1).max(10).describe("How commonly the word is used in speaking on a scale of 1 to 10."),
        examples: z.array(z.string()).describe("Examples of usage in speaking."),
        mostUsedWord: z.object({
          word: z.string().describe("The most commonly used related word in speaking."),
          example: z.string().describe("An example sentence using the most common word."),
        }),
      }),
  
      writing: z.object({
        frequency: z.number().min(1).max(10).describe("How commonly the word is used in writing on a scale of 1 to 10."),
        examples: z.array(z.string()).describe("Examples of usage in writing."),
        mostUsedWord: z.object({
          word: z.string().describe("The most commonly used related word in writing."),
          example: z.string().describe("An example sentence using the most common word."),
        }),
      }),
    }).describe("Usage frequency of the word in both speaking and writing."),
  })).describe("curated list of 5-10 English words or phrases strongly related to this theme"),

  words:z.array(z.object({word: z.string().describe("The word being described."),

  primaryMeaning: z.array(
    z.object({
      meaning: z.string().describe("The primary meaning of the word."),
      translation: z.string().describe("French translation of the primary meaning."),
    })
  ),

  exampleSentences: z.array(
    z.object({
      sentence: z.string().describe("Example sentence."),
      translation: z.string().describe("French translation of the example sentence."),
    })
  ),

  otherMeanings: z.array(
    z.object({
      meaning: z.string().describe("Other meanings of the word."),
      example: z.string().describe("One example sentence for this meaning."),
    })
  ),

  synonyms: z.array(z.string()).describe("A few synonyms of the word."),

  usageFrequency: z.object({
    speaking: z.object({
      frequency: z.number().min(1).max(10).describe("How commonly the word is used in speaking on a scale of 1 to 10."),
      examples: z.array(z.string()).describe("Examples of usage in speaking."),
      mostUsedWord: z.object({
        word: z.string().describe("The most commonly used related word in speaking."),
        example: z.string().describe("An example sentence using the most common word."),
      }),
    }),

    writing: z.object({
      frequency: z.number().min(1).max(10).describe("How commonly the word is used in writing on a scale of 1 to 10."),
      examples: z.array(z.string()).describe("Examples of usage in writing."),
      mostUsedWord: z.object({
        word: z.string().describe("The most commonly used related word in writing."),
        example: z.string().describe("An example sentence using the most common word."),
      }),
    }),
  }).describe("Usage frequency of the word in both speaking and writing."),
})).describe("curated list of 5-10 English words or phrases strongly related to this theme")

});

//TODO add mnemotechnic
//TODO verb-noun-adjective-adverb connections
//TODO antonym synonym
export const mnemonic = z.object({
  keywordMethod:z.array(z.string().describe(` The Keyword Method
    Identify a keyword: Find a word (in your native language) that sounds similar to the foreign word you want to learn.
    Create an image or mental link: Imagine a scenario that connects the sound-alike (keyword) to the meaning of the foreign word. compare the sound with french sound`)).describe("mnemonic to describe the word"),//TODO describe
  associationAndVisualization:z.array(z.string().describe(` The Association and Visualization
    Use imagination to create vivid images: Associate the word’s sound or meaning with a memorable mental picture.
    Tie it to personal experiences: If possible, connect the word with something personally meaningful (a friend’s name, a place you’ve visited, etc.).my primary laguage is french`)).describe("mnemonic to describe the word")
})

export const correctionSchema = z.object({
  possibilties:z.array(z.string()).describe("propose me here other possibilities to express the same query don't respose to the query"),
  correctedSentence:z.object({
    error:z.boolean(),
    sentence:z.string().describe("the corrected sentence bold(*...*) the place where there are error "),
    explanation:z.string().describe("the explanation of the error made ")
  }).nullable().describe("if the sentence is correct return null")
})


export const nouns = z.object({
  usage:z.object({
    hightFrequency:z.array(z.object({
      noun:z.string().describe("the noun"),
      mnemonic,
      derivations:z.object({
        verbs:z.array(z.string()).optional().describe("verbs derivated from this noun"),
        adjectives:z.array(z.string()).optional().describe("adjectives derivated from this noun"),
        adverbs:z.array(z.string()).optional().describe("adverbs derivated from this noun"),
      }).optional(),
      synonyms:z.array(z.object({
        synonym:z.string().describe("the synonym"),
        meaning:z.string().describe("the translation of the synonym in french"),
        IPASpelling:z.string().describe("the IPA spelling of the synonym"),
      })).describe("the synonyms of the adjective"),
      antonyms:z.array(z.object({
        antonym:z.string().describe("the antonym"),
        meaning:z.string().describe("the translation of the antonym in french"),
        IPASpelling:z.string().describe("the IPA spelling of the antonym"),
      })).describe("the antonyms of the adjective"),
      IPASpelling:z.string().describe("the IPA spelling of the noun"),
      meaning:z.string().describe("french meaning of the word"),
      collocations:z.array(z.object({
        collocation:z.string().describe("the collocation"),
        meaning:z.string().describe("the translation of the sentence in french"),
        IPASpelling:z.string().describe("the IPA spelling of the noun"),
      })).describe("the collocations of the noun"),

    })),
    middleFrequency:z.array(z.object({
      noun:z.string().describe("the noun"),
      mnemonic,
      derivations:z.object({
        verbs:z.array(z.string()).optional().describe("verbs derivated from this noun"),
        adjectives:z.array(z.string()).optional().describe("adjectives derivated from this noun"),
        adverbs:z.array(z.string()).optional().describe("adverbs derivated from this noun"),
      }).optional(),
      synonyms:z.array(z.object({
        synonym:z.string().describe("the synonym"),
        meaning:z.string().describe("the translation of the synonym in french"),
        IPASpelling:z.string().describe("the IPA spelling of the synonym"),
      })).describe("the synonyms of the adjective"),
      antonyms:z.array(z.object({
        antonym:z.string().describe("the antonym"),
        meaning:z.string().describe("the translation of the antonym in french"),
        IPASpelling:z.string().describe("the IPA spelling of the antonym"),
      })).describe("the antonyms of the adjective"),
      IPASpelling:z.string().describe("the IPA spelling of the noun"),
      meaning:z.string().describe("french meaning of the word"),
      collocations:z.array(z.object({
        collocation:z.string().describe("the collocation"),
        meaning:z.string().describe("the translation of the sentence in french"),
        IPASpelling:z.string().describe("the IPA spelling of the noun"),
      })).describe("the collocations of the noun"),
    }))
  })
})

export const nounsGen = z.object({
  usage:z.object({
    hightFrequency:z.array(z.object({
      noun:z.string().describe("the noun "),
      derivations:z.object({
        verbs:z.array(z.string()).optional().describe("verbs derivated from this noun"),
        adjectives:z.array(z.string()).optional().describe("adjectives derivated from this noun"),
        adverbs:z.array(z.string()).optional().describe("adverbs derivated from this noun"),
      }).optional(),
      synonyms:z.array(z.object({
        synonym:z.string().describe("the synonym"),
        meaning:z.string().describe("the translation of the synonym in french"),
        IPASpelling:z.string().describe("the IPA spelling of the synonym"),
      })).describe("the synonyms of the adjective"),
      antonyms:z.array(z.object({
        antonym:z.string().describe("the antonym"),
        meaning:z.string().describe("the translation of the antonym in french"),
        IPASpelling:z.string().describe("the IPA spelling of the antonym"),
      })).describe("the antonyms of the adjective"),
      IPASpelling:z.string().describe("the IPA spelling of the noun"),
      meaning:z.string().describe("french meaning of the word"),
      collocations:z.array(z.object({
        collocation:z.string().describe("the collocation"),
        meaning:z.string().describe("the translation of the sentence in french"),
        IPASpelling:z.string().describe("the IPA spelling of the noun"),
      })).describe("the collocations of the noun. List all possibles combinaisons if exist ( Verb + Noun, Adjective + Noun, Preposition + Noun)"),

    })),
    middleFrequency:z.array(z.object({
      noun:z.string().describe("the noun"),
      derivations:z.object({
        verbs:z.array(z.string()).optional().describe("verbs derivated from this noun"),
        adjectives:z.array(z.string()).optional().describe("adjectives derivated from this noun"),
        adverbs:z.array(z.string()).optional().describe("adverbs derivated from this noun"),
      }).optional(),
      synonyms:z.array(z.object({
        synonym:z.string().describe("the synonym"),
        meaning:z.string().describe("the translation of the synonym in french"),
        IPASpelling:z.string().describe("the IPA spelling of the synonym"),
      })).describe("the synonyms of the adjective"),
      antonyms:z.array(z.object({
        antonym:z.string().describe("the antonym"),
        meaning:z.string().describe("the translation of the antonym in french"),
        IPASpelling:z.string().describe("the IPA spelling of the antonym"),
      })).describe("the antonyms of the adjective"),
      IPASpelling:z.string().describe("the IPA spelling of the noun"),
      meaning:z.string().describe("french meaning of the word"),
      collocations:z.array(z.object({
        collocation:z.string().describe("the collocation"),
        meaning:z.string().describe("the translation of the sentence in french"),
        IPASpelling:z.string().describe("the IPA spelling of the noun"),
      })).describe("the collocations of the noun. List all possibles combinaisons if exist ( Verb + Noun, Adjective + Noun, Preposition + Noun) "),
    }))
  })
})

export const verbs = z.object({
  usage:z.object({
    hightFrequency:z.array(z.object({
      verb:z.string().describe("the verb"),
      mnemonic,
      derivations:z.object({
        nouns:z.array(z.string()).optional().describe("nouns derivated from this verb"),
        adjectives:z.array(z.string()).optional().describe("adjectives derivated from this verb"),
        adverbs:z.array(z.string()).optional().describe("adverbs derivated from this verb"),
      }).optional(),
      synonyms:z.array(z.object({
        synonym:z.string().describe("the synonym"),
        meaning:z.string().describe("the translation of the synonym in french"),
        IPASpelling:z.string().describe("the IPA spelling of the synonym"),
      })).describe("the synonyms of the adjective"),
      antonyms:z.array(z.object({
        antonym:z.string().describe("the antonym"),
        meaning:z.string().describe("the translation of the antonym in french"),
        IPASpelling:z.string().describe("the IPA spelling of the antonym"),
      })).describe("the antonyms of the adjective"),
      IPASpelling:z.string().describe("the IPA spelling of the verb"),
      meaning:z.string().describe("french meaning of the word"),
      collocations:z.array(z.object({
        collocation:z.string().describe("the collocation"),
        meaning:z.string().describe("the translation of the sentence in french"),
        IPASpelling:z.string().describe("the IPA spelling of the verb"),
      })).describe("the collocations of the verb"),

    })),
    middleFrequency:z.array(z.object({
      verb:z.string().describe("the verb"),
      mnemonic,
      derivations:z.object({
        nouns:z.array(z.string()).optional().describe("nouns derivated from this verb"),
        adjectives:z.array(z.string()).optional().describe("adjectives derivated from this verb"),
        adverbs:z.array(z.string()).optional().describe("adverbs derivated from this verb"),
      }).optional(),
      synonyms:z.array(z.object({
        synonym:z.string().describe("the synonym"),
        meaning:z.string().describe("the translation of the synonym in french"),
        IPASpelling:z.string().describe("the IPA spelling of the synonym"),
      })).describe("the synonyms of the adjective"),
      antonyms:z.array(z.object({
        antonym:z.string().describe("the antonym"),
        meaning:z.string().describe("the translation of the antonym in french"),
        IPASpelling:z.string().describe("the IPA spelling of the antonym"),
      })).describe("the antonyms of the adjective"),
      IPASpelling:z.string().describe("the IPA spelling of the verb"),
      meaning:z.string().describe("french meaning of the word"),
      collocations:z.array(z.object({
        collocation:z.string().describe("the collocation"),
        meaning:z.string().describe("the translation of the sentence in french"),
        IPASpelling:z.string().describe("the IPA spelling of the verb"),
      })).describe("the collocations of the verb"),
    }))
  })
})

export const verbsGen = z.object({
  usage:z.object({
    hightFrequency:z.array(z.object({
      verb:z.string().describe("the verb"),
      derivations:z.object({
        nouns:z.array(z.string()).optional().describe("nouns derivated from this verb"),
        adjectives:z.array(z.string()).optional().describe("adjectives derivated from this verb"),
        adverbs:z.array(z.string()).optional().describe("adverbs derivated from this verb"),
      }).optional(),
      synonyms:z.array(z.object({
        synonym:z.string().describe("the synonym"),
        meaning:z.string().describe("the translation of the synonym in french"),
        IPASpelling:z.string().describe("the IPA spelling of the synonym"),
      })).describe("the synonyms of the adjective"),
      antonyms:z.array(z.object({
        antonym:z.string().describe("the antonym"),
        meaning:z.string().describe("the translation of the antonym in french"),
        IPASpelling:z.string().describe("the IPA spelling of the antonym"),
      })).describe("the antonyms of the adjective"),
      IPASpelling:z.string().describe("the IPA spelling of the verb"),
      meaning:z.string().describe("french meaning of the word"),
      collocations:z.array(z.object({
        collocation:z.string().describe("the collocation"),
        meaning:z.string().describe("the translation of the sentence in french"),
        IPASpelling:z.string().describe("the IPA spelling of the verb"),
      })).describe("the collocations of the verb. List all possibles combinaisons if exist ( Verb + Noun, Adjective + Noun, Preposition + Noun)"),

    })).describe("provide the verbs with and without prepostions."),
    middleFrequency:z.array(z.object({
      verb:z.string().describe("the verb"),
      derivations:z.object({
        nouns:z.array(z.string()).optional().describe("nouns derivated from this verb"),
        adjectives:z.array(z.string()).optional().describe("adjectives derivated from this verb"),
        adverbs:z.array(z.string()).optional().describe("adverbs derivated from this verb"),
      }).optional(),
      synonyms:z.array(z.object({
        synonym:z.string().describe("the synonym"),
        meaning:z.string().describe("the translation of the synonym in french"),
        IPASpelling:z.string().describe("the IPA spelling of the synonym"),
      })).describe("the synonyms of the adjective"),
      antonyms:z.array(z.object({
        antonym:z.string().describe("the antonym"),
        meaning:z.string().describe("the translation of the antonym in french"),
        IPASpelling:z.string().describe("the IPA spelling of the antonym"),
      })).describe("the antonyms of the adjective"),
      IPASpelling:z.string().describe("the IPA spelling of the verb"),
      meaning:z.string().describe("french meaning of the word"),
      collocations:z.array(z.object({
        collocation:z.string().describe("the collocation"),
        meaning:z.string().describe("the translation of the sentence in french"),
        IPASpelling:z.string().describe("the IPA spelling of the verb"),
      })).describe("the collocations of the verb . List all possibles combinaisons if exist ( Verb + Noun, Adjective + Noun, Preposition + Noun)"),
    })).describe("provide the verbs with and without prepostions.")
  })
})

export const adjectives = z.object({
  usage:z.object({
    hightFrequency:z.array(z.object({
      adjective:z.string().describe("the adjective"),
      mnemonic,
      derivations:z.object({
        nouns:z.array(z.string()).optional().describe("nouns derivated from this adjective"),
        verbs:z.array(z.string()).optional().describe("verbs derivated from this adjective"),
        adverbs:z.array(z.string()).optional().describe("adverbs derivated from this adjective"),
      }).optional(),
      synonyms:z.array(z.object({
        synonym:z.string().describe("the synonym"),
        meaning:z.string().describe("the translation of the synonym in french"),
        IPASpelling:z.string().describe("the IPA spelling of the synonym"),
      })).describe("the synonyms of the adjective"),
      antonyms:z.array(z.object({
        antonym:z.string().describe("the antonym"),
        meaning:z.string().describe("the translation of the antonym in french"),
        IPASpelling:z.string().describe("the IPA spelling of the antonym"),
      })).describe("the antonyms of the adjective"),
      IPASpelling:z.string().describe("the IPA spelling of the adjective"),
      meaning:z.string().describe("french meaning of the word"),
      collocations:z.array(z.object({
        collocation:z.string().describe("the collocation"),
        meaning:z.string().describe("the translation of the sentence in french"),
        IPASpelling:z.string().describe("the IPA spelling of the adjective"),
      })).describe("the collocations of the adjective"),

    })),
    middleFrequency:z.array(z.object({
      adjective:z.string().describe("the adjective"),
      mnemonic,
      derivations:z.object({
        nouns:z.array(z.string()).optional().describe("nouns derivated from this adjective"),
        verbs:z.array(z.string()).optional().describe("verbs derivated from this adjective"),
        adverbs:z.array(z.string()).optional().describe("adverbs derivated from this adjective"),
      }).optional(),
      synonyms:z.array(z.object({
        synonym:z.string().describe("the synonym"),
        meaning:z.string().describe("the translation of the synonym in french"),
        IPASpelling:z.string().describe("the IPA spelling of the synonym"),
      })).describe("the synonyms of the adjective"),
      antonyms:z.array(z.object({
        antonym:z.string().describe("the antonym"),
        meaning:z.string().describe("the translation of the antonym in french"),
        IPASpelling:z.string().describe("the IPA spelling of the antonym"),
      })).describe("the antonyms of the adjective"),
      IPASpelling:z.string().describe("the IPA spelling of the adjective"),
      meaning:z.string().describe("french meaning of the word"),
      collocations:z.array(z.object({
        collocation:z.string().describe("the collocation"),
        meaning:z.string().describe("the translation of the sentence in french"),
        IPASpelling:z.string().describe("the IPA spelling of the adjective"),
      })).describe("the collocations of the adjective"),
    }))
  })
})

export const adjectivesGen = z.object({
  usage:z.object({
    hightFrequency:z.array(z.object({
      adjective:z.string().describe("the adjective"),
      derivations:z.object({
        nouns:z.array(z.string()).optional().describe("nouns derivated from this adjective"),
        verbs:z.array(z.string()).optional().describe("verbs derivated from this adjective"),
        adverbs:z.array(z.string()).optional().describe("adverbs derivated from this adjective"),
      }).optional(),
      synonyms:z.array(z.object({
        synonym:z.string().describe("the synonym"),
        meaning:z.string().describe("the translation of the synonym in french"),
        IPASpelling:z.string().describe("the IPA spelling of the synonym"),
      })).describe("the synonyms of the adjective"),
      antonyms:z.array(z.object({
        antonym:z.string().describe("the antonym"),
        meaning:z.string().describe("the translation of the antonym in french"),
        IPASpelling:z.string().describe("the IPA spelling of the antonym"),
      })).describe("the antonyms of the adjective"),
      IPASpelling:z.string().describe("the IPA spelling of the adjective"),
      meaning:z.string().describe("french meaning of the word"),
      collocations:z.array(z.object({
        collocation:z.string().describe("the collocation"),
        meaning:z.string().describe("the translation of the sentence in french"),
        IPASpelling:z.string().describe("the IPA spelling of the adjective"),
      })).describe("the collocations of the adjective. List all possibles combinaisons if exist ( Verb + Noun, Adjective + Noun, Preposition + Noun)"),

    })),
    middleFrequency:z.array(z.object({
      adjective:z.string().describe("the adjective"),
      derivations:z.object({
        nouns:z.array(z.string()).optional().describe("nouns derivated from this adjective"),
        verbs:z.array(z.string()).optional().describe("verbs derivated from this adjective"),
        adverbs:z.array(z.string()).optional().describe("adverbs derivated from this adjective"),
      }).optional(),
      synonyms:z.array(z.object({
        synonym:z.string().describe("the synonym"),
        meaning:z.string().describe("the translation of the synonym in french"),
        IPASpelling:z.string().describe("the IPA spelling of the synonym"),
      })).describe("the synonyms of the adjective"),
      antonyms:z.array(z.object({
        antonym:z.string().describe("the antonym"),
        meaning:z.string().describe("the translation of the antonym in french"),
        IPASpelling:z.string().describe("the IPA spelling of the antonym"),
      })).describe("the antonyms of the adjective"),
      IPASpelling:z.string().describe("the IPA spelling of the adjective"),
      meaning:z.string().describe("french meaning of the word"),
      collocations:z.array(z.object({
        collocation:z.string().describe("the collocation"),
        meaning:z.string().describe("the translation of the sentence in french"),
        IPASpelling:z.string().describe("the IPA spelling of the adjective"),
      })).describe("the collocations of the adjective. List all possibles combinaisons if exist ( Verb + Noun, Adjective + Noun, Preposition + Noun)"),
    }))
  })
})

export const adverbs = z.object({
  usage:z.object({
    hightFrequency:z.array(z.object({
      adverb:z.string().describe("the adverb"),
      mnemonic,
      derivations:z.object({
        nouns:z.array(z.string()).optional().describe("nouns derivated from this adverb"),
        verbs:z.array(z.string()).optional().describe("verbs derivated from this adverb"),
        adjectives:z.array(z.string()).optional().describe("adjectives derivated from this adverb"),
      }).optional(),
      synonyms:z.array(z.object({
        synonym:z.string().describe("the synonym"),
        meaning:z.string().describe("the translation of the synonym in french"),
        IPASpelling:z.string().describe("the IPA spelling of the synonym"),
      })).describe("the synonyms of the adjective"),
      antonyms:z.array(z.object({
        antonym:z.string().describe("the antonym"),
        meaning:z.string().describe("the translation of the antonym in french"),
        IPASpelling:z.string().describe("the IPA spelling of the antonym"),
      })).describe("the antonyms of the adjective"),
      IPASpelling:z.string().describe("the IPA spelling of the adverb"),
      meaning:z.string().describe("french meaning of the word"),
      collocations:z.array(z.object({
        collocation:z.string().describe("the collocation"),
        meaning:z.string().describe("the translation of the sentence in french"),
        IPASpelling:z.string().describe("the IPA spelling of the adverb"),
      })).describe("the collocations of the adverb"),

    })),
    middleFrequency:z.array(z.object({
      adverb:z.string().describe("the adverb"),
      mnemonic,
      derivations:z.object({
        nouns:z.array(z.string()).optional().describe("nouns derivated from this adverb"),
        verbs:z.array(z.string()).optional().describe("verbs derivated from this adverb"),
        adjectives:z.array(z.string()).optional().describe("adjectives derivated from this adverb"),
      }).optional(),
      synonyms:z.array(z.object({
        synonym:z.string().describe("the synonym"),
        meaning:z.string().describe("the translation of the synonym in french"),
        IPASpelling:z.string().describe("the IPA spelling of the synonym"),
      })).describe("the synonyms of the adjective"),
      antonyms:z.array(z.object({
        antonym:z.string().describe("the antonym"),
        meaning:z.string().describe("the translation of the antonym in french"),
        IPASpelling:z.string().describe("the IPA spelling of the antonym"),
      })).describe("the antonyms of the adjective"),
      IPASpelling:z.string().describe("the IPA spelling of the adverb"),
      meaning:z.string().describe("french meaning of the word"),
      collocations:z.array(z.object({
        collocation:z.string().describe("the collocation"),
        meaning:z.string().describe("the translation of the sentence in french"),
        IPASpelling:z.string().describe("the IPA spelling of the adverb"),
      })).describe("the collocations of the adverb"),
    }))
  })
})

export const adverbsGen = z.object({
  usage:z.object({
    hightFrequency:z.array(z.object({
      adverb:z.string().describe("the adverb"),
      derivations:z.object({
        nouns:z.array(z.string()).optional().describe("nouns derivated from this adverb"),
        verbs:z.array(z.string()).optional().describe("verbs derivated from this adverb"),
        adjectives:z.array(z.string()).optional().describe("adjectives derivated from this adverb"),
      }).optional(),
      synonyms:z.array(z.object({
        synonym:z.string().describe("the synonym"),
        meaning:z.string().describe("the translation of the synonym in french"),
        IPASpelling:z.string().describe("the IPA spelling of the synonym"),
      })).describe("the synonyms of the adjective"),
      antonyms:z.array(z.object({
        antonym:z.string().describe("the antonym"),
        meaning:z.string().describe("the translation of the antonym in french"),
        IPASpelling:z.string().describe("the IPA spelling of the antonym"),
      })).describe("the antonyms of the adjective"),
      IPASpelling:z.string().describe("the IPA spelling of the adverb"),
      meaning:z.string().describe("french meaning of the word"),
      collocations:z.array(z.object({
        collocation:z.string().describe("the collocation"),
        meaning:z.string().describe("the translation of the sentence in french"),
        IPASpelling:z.string().describe("the IPA spelling of the adverb"),
      })).describe("the collocations of the adverb. List all possibles combinaisons if exist ( Verb + Noun, Adjective + Noun, Preposition + Noun)"),

    })),
    middleFrequency:z.array(z.object({
      adverb:z.string().describe("the adverb"),
      derivations:z.object({
        nouns:z.array(z.string()).optional().describe("nouns derivated from this adverb"),
        verbs:z.array(z.string()).optional().describe("verbs derivated from this adverb"),
        adjectives:z.array(z.string()).optional().describe("adjectives derivated from this adverb"),
      }).optional(),
      synonyms:z.array(z.object({
        synonym:z.string().describe("the synonym"),
        meaning:z.string().describe("the translation of the synonym in french"),
        IPASpelling:z.string().describe("the IPA spelling of the synonym"),
      })).describe("the synonyms of the adjective"),
      antonyms:z.array(z.object({
        antonym:z.string().describe("the antonym"),
        meaning:z.string().describe("the translation of the antonym in french"),
        IPASpelling:z.string().describe("the IPA spelling of the antonym"),
      })).describe("the antonyms of the adjective"),
      IPASpelling:z.string().describe("the IPA spelling of the adverb"),
      meaning:z.string().describe("french meaning of the word"),
      collocations:z.array(z.object({
        collocation:z.string().describe("the collocation"),
        meaning:z.string().describe("the translation of the sentence in french"),
        IPASpelling:z.string().describe("the IPA spelling of the adverb"),
      })).describe("the collocations of the adverb. List all possibles combinaisons if exist ( Verb + Noun, Adjective + Noun, Preposition + Noun)"),
    }))
  })
})

export const newThemeDiscover = z.object({
  theme:z.string().describe("The theme being described."),
  subCategory:z.union([z.array(z.string()).describe("The subcategory of the theme being described."),z.string()]),
  nouns : z.union([z.object({
    nouns:nounsGen,
    mnemonic:z.object({
      hightFrequency:mnemonic,
      middleFrequency:mnemonic
    })
  }), z.string()]),
  verbs : z.union([z.object({
    verbs : verbsGen,
    mnemonic:z.object({
      hightFrequency:mnemonic,
      middleFrequency:mnemonic
    })
  }), z.string()]) ,
  adjectives : z.union([z.object({
    adjectives : adjectivesGen,
    mnemonic:z.object({
      hightFrequency:mnemonic,
      middleFrequency:mnemonic
    })
  }),z.string()]),
  adverbs : z.union([z.object({
    adverbs:adverbsGen,
    mnemonic:z.object({
      hightFrequency:mnemonic,
      middleFrequency:mnemonic
    })
  }) , z.string()]),
});


export const schemaThemes = z.object({
  themes:z.array(
      z.object({
          theme:z.string(),
          description:z.string()
      })
  ).describe("array of themes proposition")
})
 //TODO change name
  export const r = z.object({
    adverb:z.string().describe("the adverb").optional(),
    noun:z.string().describe("the noun").optional(),
    verb:z.string().describe("the verb").optional(),
    adjective:z.string().describe("the adjective").optional(),
    derivations:z.object({
      nouns:z.array(z.string()).optional().describe("nouns derivated from this adverb"),
      verbs:z.array(z.string()).optional().describe("verbs derivated from this adverb"),
      adjectives:z.array(z.string()).optional().describe("adjectives derivated from this adverb"),
    }).optional(),
    synonyms:z.array(z.object({
      synonym:z.string().describe("the synonym"),
      meaning:z.string().describe("the translation of the synonym in french"),
      IPASpelling:z.string().describe("the IPA spelling of the synonym"),
    })).describe("the synonyms of the adjective"),
    antonyms:z.array(z.object({
      antonym:z.string().describe("the antonym"),
      meaning:z.string().describe("the translation of the antonym in french"),
      IPASpelling:z.string().describe("the IPA spelling of the antonym"),
    })).describe("the antonyms of the adjective"),
    IPASpelling:z.string().describe("the IPA spelling of the adverb"),
    meaning:z.string().describe("french meaning of the word"),
    collocations:z.array(z.object({
      collocation:z.string().describe("the collocation"),
      meaning:z.string().describe("the translation of the sentence in french"),
      IPASpelling:z.string().describe("the IPA spelling of the adverb"),
    })).describe("the collocations of the adverb"),
  })

export const messageSchema = z.object({
  role: z.enum(["user", "assistant"]),
  content: z.string(),
});

export const chatSectionSchema = z.object({
  content: z.string(),
  grammarRules: z.array(z.object({
    grammarRuleId: z.string(),
    weight: z.number().int().min(0),
  })),
  messages: z.array(messageSchema),
});

export const vocabularyResponseSchema = z.object({
  suggestion: z.object({
    grammar_rule: z.string(),
    next_response_ideas: z.array(z.string()),
    feedback: z.string().optional(),
  }),
  message: z.string(),
});

export const correctionResponseSchema = z.object({
  possibilties: z.array(z.string()),
  correctedSentence: z.object({
    error: z.boolean(),
    sentence: z.string(),
    explanation: z.string(),
  }).nullable(),
});